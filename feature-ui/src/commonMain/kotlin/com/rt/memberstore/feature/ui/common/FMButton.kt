/*
 * Copyright (c) 2025. 康成投资（中国）有限公司
 * http://www.rt-mart.com
 * 版权归本公司所有，不得私自使用、拷贝、修改、删除，否则视为侵权
 */

package com.rt.memberstore.feature.ui.common

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.rt.memberstore.feature.ui.theme.FMColor
import memberstore.feature_ui.generated.resources.Res
import memberstore.feature_ui.generated.resources.ms_add_cart
import org.jetbrains.compose.resources.stringResource

/**
 * 通用按钮
 * 渐变红色背景、白字体、16大小
 *
 * @param onClick 点击事件回调
 * @param enabled 是否可用
 * @param modifier 修饰符
 */
@Composable
fun FMButton(
    text: String ,
    onClick: () -> Unit,
    enabled: Boolean = true,
    modifier: Modifier = Modifier.padding(start = 21.5.dp)
) {
    // 创建渐变背景 - 从左到右 F4003B 到 E40137
    val gradient = Brush.horizontalGradient(
        colors = listOf(
            FMColor.color_f4003b,
            FMColor.color_e40137,
        )
    )

    // 禁用状态的背景色
    val disabledColor = FMColor.color_dedede

    Box(
        modifier = modifier
            .height(39.dp)
            .clip(RoundedCornerShape(6.dp))
            .let {
                if (enabled) {
                    it.background(gradient)
                } else {
                    it.background(disabledColor)
                }
            }

            .padding(horizontal = 10.dp, vertical = 8.5.dp)
            .clickable(enabled = enabled) { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            color = FMColor.color_white,
            fontSize = 16.sp,
            lineHeight = 16.sp,
            fontWeight = FontWeight.Bold
        )
    }
}
