/*
 * Copyright (c) 2025. 康成投资（中国）有限公司
 * http://www.rt-mart.com
 * 版权归本公司所有，不得私自使用、拷贝、修改、删除，否则视为侵权
 */

package com.rt.memberstore.feature.ui.order.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.fn.kt.lib.loading.LoadingView
import com.fn.kt.lib.toast.Toast
import com.rt.kt.lib.core.presentation.components.statusbox.UIState
import com.rt.kt.lib.core.presentation.components.statusbox.rememberPageState
import com.rt.memberstore.feature.logic.domain.integration.order.RefundListComponent
import com.rt.memberstore.feature.logic.domain.integration.order.RefundListComponent.Output
import com.rt.memberstore.feature.logic.domain.store.order.RefundListStore
import com.rt.memberstore.feature.ui.common.FooterStatus
import com.rt.memberstore.feature.ui.common.ListFooter
import com.rt.memberstore.feature.ui.common.NoDataView
import com.rt.memberstore.feature.ui.common.base.FMPageRoot
import com.rt.memberstore.feature.ui.common.isScrolledToTheEnd
import com.rt.kt.lib.component.refreshlayout.RefreshContentStateEnum
import com.rt.kt.lib.component.refreshlayout.RefreshLayoutState
import com.rt.kt.lib.component.refreshlayout.rememberRefreshLayoutState
import com.rt.memberstore.feature.ui.common.refreshlayout.FMPullToRefreshLayout
import com.rt.memberstore.feature.ui.order.view.RefundListItem
import com.rt.memberstore.feature.ui.theme.FMColor
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import memberstore.feature_ui.generated.resources.Res
import memberstore.feature_ui.generated.resources.pic_empty_4
import memberstore.feature_ui.generated.resources.refund_empty_list
import memberstore.feature_ui.generated.resources.refund_list_title
import org.jetbrains.compose.resources.stringResource

/**
 * 退订单列表屏幕
 * 展示退订单列表并处理用户交互
 *
 * @param component 退订列表组件实例，提供数据和处理用户操作
 */
@Composable
fun RefundListScreen(
    component: RefundListComponent
) {
    // 获取组件状态
    val state by component.state.collectAsState()

    // 下拉刷新状态
    val refreshState = rememberRefreshLayoutState {
        component.onIntent(RefundListStore.Intent.LoadRefundList(refresh = true))
    }
    // 状态切换器
    val stateContainer = rememberPageState(UIState.Success(Unit))

    // 接收处理Label
    LaunchedEffect(Unit) {
        component.labels.collect {
            when (it) {
                is RefundListStore.Label.ShowToast -> {
                    // 弹Toast
                    Toast.showShort(it.message)
                }

                is RefundListStore.Label.OnLoadComplete -> {
                    // 加载完成，更新下拉刷新状态
                    refreshState.setRefreshState(RefreshContentStateEnum.Stop)
                }
            }
        }
    }

    // 页面根容器
    FMPageRoot(
        title = stringResource(Res.string.refund_list_title),
        onBackClick = { component.onOutput(Output.Close) },
        stateContainer = stateContainer,
        pageInitDataLogic = {
            component.onIntent(RefundListStore.Intent.LoadRefundList(refresh = true))
            stateContainer.changeState(UIState.Success(Unit))
        },
        contentScrollEnabled = false,
        rootPageColumnModifier = { modifier ->
            modifier.fillMaxSize().background(FMColor.color_f2f2f2)
        }
    ) {
        RefundListPage(
            state = state,
            refreshState = refreshState,
            onIntent = component::onIntent,
            onOutput = component::onOutput
        )
    }

    // 页面的Loading
    if (state.showLoading) {
//        LoadingDialog() 页面初始化时有点问题，待修复
        LoadingView()
    }
}

/**
 * 退款订单列表页面，除外层的主体
 * @param state 退款订单列表状态
 * @param pullRefreshState 下拉刷新状态
 * @param onRefresh 刷新回调
 * @param onIntent 处理意图的回调
 * @param onOutput 处理输出的回调
 */
@Composable
private fun RefundListPage(
    state: RefundListStore.State,
    refreshState: RefreshLayoutState,
    onIntent: (RefundListStore.Intent) -> Unit,
    onOutput: (Output) -> Unit
) {
    // 主内容区域
    FMPullToRefreshLayout(
        refreshLayoutState = refreshState,
        modifier = Modifier.fillMaxSize().background(FMColor.color_f2f2f2),
    ) {
        if (state.showNoData) {
            // 无数据
            RefundListEmpty()
        } else {
            // 有数据
            RefundListContent(state = state, onIntent = onIntent, onOutput = onOutput)
        }
    }
}

/**
 * 空数据页
 */
@Composable
private fun RefundListEmpty() {
    Column(
        modifier = Modifier.fillMaxSize().verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        NoDataView(
            image = Res.drawable.pic_empty_4,
            content = stringResource(Res.string.refund_empty_list),
        )
    }
}

/**
 * 退款订单列表
 */
@Composable
private fun RefundListContent(
    state: RefundListStore.State,
    onIntent: (RefundListStore.Intent) -> Unit,
    onOutput: (Output) -> Unit
) {
    val listState = rememberLazyListState()

    // 加载更多数据
    LaunchedEffect(listState, state.currentPage, state.hasNextPage) {
        if (!state.hasNextPage) return@LaunchedEffect
        // 滑动到底部时加载更多数据
        snapshotFlow { listState.isScrolledToTheEnd() }
            .distinctUntilChanged()
            .filter { it }
            .collect {
                onIntent(RefundListStore.Intent.LoadRefundList(refresh = false))
            }
    }

    // 从填写退货信息页返回后刷新，需要滑动到顶部
    LaunchedEffect(listState, state.needScrollToTop) {
        if (state.needScrollToTop) {
            listState.scrollToItem(0)
            onIntent(RefundListStore.Intent.ResetNeedScrollToTop)
        }
    }

    // 列表内容
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        state = listState,
        userScrollEnabled = true
    ) {
        // 退订单项列表
        itemsIndexed(state.refundList) { _, item ->
            // 退订单项视图
            RefundListItem(
                orderVo = item,
                onIntent = onIntent,
                onOutput = onOutput
            )
        }

        // 底部加载更多指示器
        if (state.refundList.isNotEmpty()) {
            item {
                ListFooter(
                    status = if (state.hasNextPage) FooterStatus.LOADING else FooterStatus.NO_MORE,
                )
            }
        }
    }
}
