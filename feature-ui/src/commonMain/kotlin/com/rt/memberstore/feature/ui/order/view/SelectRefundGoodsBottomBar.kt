package com.rt.memberstore.feature.ui.order.view

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.Button
import androidx.compose.material3.Checkbox
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.rt.memberstore.feature.ui.common.*
import com.rt.memberstore.feature.ui.home.view.WaterfallFlowAddCartButton
import com.rt.memberstore.feature.ui.theme.FMColor

@Composable
fun SelectRefundGoodsBottomBar(
    isAllSelected: Boolean,
    hasSelectedGoods: <PERSON>olean,
    onSelectAllClick: () -> Unit,
    onConfirmClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(FMColor.color_white)
            .padding(horizontal = 16.dp, vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Checkbox(
            checked = isAllSelected,
            onCheckedChange = { onSelectAllClick() }
        )

        Spacer(modifier = Modifier.width(8.dp))

        ExText(
            text = "全选",
            fontSize = 14.sp,
            color = FMColor.color_333333
        )

        Spacer(modifier = Modifier.weight(1f))

        FMButton(
            "确定",
            onClick = onConfirmClick,
            enabled = hasSelectedGoods,
            modifier = Modifier.width(100.dp)
        )

    }
}