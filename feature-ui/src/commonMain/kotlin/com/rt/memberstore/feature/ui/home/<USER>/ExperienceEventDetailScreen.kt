/*
 * Copyright (c) 2025. 康成投资（中国）有限公司
 * http://www.rt-mart.com
 * 版权归本公司所有，不得私自使用、拷贝、修改、删除，否则视为侵权
 */

package com.rt.memberstore.feature.ui.home.screen

import EnrollDTO
import ExperienceDetailStore
import InfoDTO
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.fn.kt.lib.loading.LoadingView
import com.fn.kt.lib.toast.Toast
import com.rt.kt.lib.core.presentation.components.statusbox.UIState
import com.rt.kt.lib.core.presentation.components.statusbox.rememberPageState
import com.rt.kt.lib.dialog.getExDialogController
import com.rt.memberstore.feature.logic.core.platform.IAdminUser
import com.rt.memberstore.feature.logic.data.remote.dto.home.ExperienceDetailEnum.BUTTON_ENABLE
import com.rt.memberstore.feature.logic.data.remote.dto.home.ExperienceDetailEnum.BUTTON_HAVE_SIGNUP
import com.rt.memberstore.feature.logic.data.remote.dto.home.ExperienceDetailPageState
import com.rt.memberstore.feature.logic.domain.integration.home.ExperienceEventDetailComponent
import com.rt.memberstore.feature.logic.domain.integration.home.ExperienceEventDetailComponent.Output
import com.rt.memberstore.feature.ui.common.FMImageView
import com.rt.memberstore.feature.ui.common.FMImageViewPlaceHolderType
import com.rt.memberstore.feature.ui.common.base.FMPageRoot
import com.rt.memberstore.feature.ui.home.dialog.experiencingEnrollDialog
import com.rt.memberstore.feature.ui.theme.FMColor
import memberstore.feature_ui.generated.resources.Res
import memberstore.feature_ui.generated.resources.experiencing_event_enroll_time
import memberstore.feature_ui.generated.resources.experiencing_event_rule_title
import memberstore.feature_ui.generated.resources.experiencing_event_title
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin

/**
 * 体验活动详情页面
 * Created by bangbang.qiu on 2025/6/20.
 */
@Composable
fun ExperienceEventDetailScreen(component: ExperienceEventDetailComponent) {

    val state by component.state.collectAsState()
    // 状态切换器
    val stateContainer = rememberPageState<Unit>(UIState.Empty())
    val dialogController = getExDialogController()
    val scrollState = rememberScrollState()
    var isLoadingView by remember { mutableStateOf(false) }
    // 错误状态下的重试回调
    val stateErrorRetry: (() -> Unit) = {
        component.loadPage()
    }

    LaunchedEffect(Unit) {
        component.labels.collect { label ->
            when (label) {
                // 显示Toast
                is ExperienceDetailStore.Label.ShowToast -> Toast.showShort(label.message)
                //  显示Loading
                is ExperienceDetailStore.Label.ShowLoading -> {
                    stateContainer.changeLoading(label.isLoadingDialog)
                    isLoadingView = label.isLoading
                }
                //  页面状态
                is ExperienceDetailStore.Label.PageState -> {
                    when (label.status) {
                        ExperienceDetailPageState.EMPTY -> stateContainer.changeState(UIState.Empty())
                        ExperienceDetailPageState.DATA -> {
                            //是否默认展示弹窗
                            if (component.popDialog) {
                                dialogController.show()
                                component.popDialog = false
                            }
                            stateContainer.changeState(UIState.Success(Unit))
                        }

                        ExperienceDetailPageState.ERR -> stateContainer.changeState(
                            UIState.Error("", action = stateErrorRetry)
                        )
                    }
                }

                ExperienceDetailStore.Label.SubmitSuccess -> {
                    component.loadPage()
                    dialogController.hide()
                    component.onOutput(Output.JumpExperienceSignupSuccessPage)
                }

                ExperienceDetailStore.Label.LoginSuccess -> {
                    dialogController.show()
                }
            }
        }
    }

    FMPageRoot(
        title = stringResource(Res.string.experiencing_event_title),
        onBackClick = { component.onOutput(Output.Close) },
        stateContainer = stateContainer,
        pageInitDataLogic = {
            component.loadPage()
            component.trackBrowse()
//            stateContainer.changeState(UIState.Success(Unit))
        }
    ) {
        Column(Modifier.fillMaxSize()) {
            // 可滚动区域（使用 weight 占据剩余空间）
            Column(
                modifier = Modifier.weight(1f)
                    .verticalScroll(scrollState) // 启用垂直滚动
            ) {
                buildListHead(
                    state.data?.activityPic,
                    state.data?.activityTitle,
                    state.data?.activityList,
                    state.data?.limitCount,
                )

                buildListBottom(
                    state.data?.activityRuleDesc,
                    state.data?.activityRuleUrl
                )
                Box(Modifier.size(20.dp))
            }

            // 底部固定按钮
            buildBottomButton(
                state.data?.buttonStatus,
                state.data?.buttonDesc,
                state.data?.buttonTips
            ) {
                component.trackSignUpButtonClick()
                // 登录检查桥接(android里面这么做的，实际走到这里不可能是未登录的)
                if (getKoin().get<IAdminUser>().checkLogin()) {
                    if (state.data?.buttonStatus == BUTTON_HAVE_SIGNUP) {
                        component.onOutput(Output.JumpExperienceSignupSuccessPage)
                    } else if (state.data?.buttonStatus == BUTTON_ENABLE) {
                        dialogController.show()
                    }
                } else {
                    component.onOutput(Output.JumpLoginPage)
                }
            }
        }
    }

    // 页面的Loading
    if (isLoadingView) {
        LoadingView()
    }

    //报名弹窗
    experiencingEnrollDialog(
        dialogController, state.data?.popuInfo, state.data?.joinType,
        onExpose = {
            component.trackSignUpPopupExpose()
        }
    ) { formRealName: String,
        formGender: Int,
        formAge: String,
        formPhone: String,
        formMark: String,
        isSelect: Boolean ->
        component.clickSignUpPopupSubmit(
            formRealName,
            formGender,
            formAge,
            formPhone,
            formMark,
            isSelect,
            state.data?.popuInfo?.randomCode ?: ""
        )
    }

}

@Composable
private fun buildListHead(
    url: String?,
    title: String?,
    activityList: List<InfoDTO>?,
    limitCount: EnrollDTO?
) {
    Column(
        modifier = Modifier
            .background(
                color = FMColor.color_white,
                shape = RoundedCornerShape(bottomEnd = 16.dp, bottomStart = 16.dp)
            )
    ) {
        FMImageView(
            url = url ?: "",
            contentScale = ContentScale.Crop,
            placeHolder = FMImageViewPlaceHolderType.WHITE,
            modifier = Modifier.fillMaxWidth()
        )
        Box(Modifier.size(20.dp))
        Text(
            text = title ?: "",
            fontSize = 22.sp,
            color = FMColor.color_black,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.fillMaxWidth()
                .padding(horizontal = 20.dp)
        )
        Box(Modifier.size(9.5.dp))
        Box(
            Modifier.height(0.5.dp).padding(start = 20.dp).fillMaxWidth()
                .background(FMColor.color_e5e5e5)
        )
        Box(Modifier.size(8.dp))
        if (!activityList.isNullOrEmpty()) {
            buildInfoList(activityList)
        }

        if (limitCount != null) {
            buildRegisterNumberInfo(limitCount)
        }

        Box(Modifier.size(20.dp))
    }
}

@Composable
private fun buildRegisterNumberInfo(limitCount: EnrollDTO) {
    Box(Modifier.padding(top = 16.dp, start = 20.dp)) {
        Row(
            Modifier.background(
                brush = Brush.horizontalGradient( // 水平渐变色（可改为 verticalGradient）
                    colors = listOf(
                        FMColor.color_white,
                        FMColor.color_fff1f1
                    ) // 可点击渐变
                ),
                shape = RoundedCornerShape(topEnd = 22.dp, bottomEnd = 22.dp)
            ).padding(top = 9.5.dp, bottom = 9.5.dp, end = 22.5.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = limitCount.registrationCount ?: "",
                fontSize = 18.sp,
                color = FMColor.color_ff003c,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.widthIn(max = 150.dp)
            )
            Box(Modifier.width(12.dp).height(1.dp))
            Box(
                modifier = Modifier.width(1.dp).height(16.dp)
                    .background(FMColor.color_ff003c)
            )
            Box(Modifier.width(12.dp).height(1.dp))
            Text(
                text = limitCount.registrationLimitCount ?: "",
                fontSize = 14.sp,
                color = FMColor.color_ff003c,
            )
        }
    }
}

@Composable
private fun buildListBottom(activityRuleDesc: String?, activityRuleUrl: String?) {
    Column(Modifier.fillMaxWidth().padding(top = 20.dp)) {
        Text(
            text = stringResource(Res.string.experiencing_event_rule_title),
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(horizontal = 20.dp),
            color = FMColor.color_black
        )
        Box(Modifier.size(10.dp))
        if (!activityRuleDesc.isNullOrEmpty()) {
            Text(
                text = activityRuleDesc,
                fontSize = 13.sp,
                modifier = Modifier.padding(horizontal = 20.dp),
                color = FMColor.color_666666
            )
        }
        if (!activityRuleUrl.isNullOrEmpty()) {
            FMImageView(
                url = activityRuleUrl,
                contentScale = ContentScale.FillWidth,
                placeHolder = FMImageViewPlaceHolderType.WHITE,
                modifier = Modifier.fillMaxWidth()
            )
        }

    }
}

@Composable
private fun buildBottomButton(
    buttonStatus: Int?,
    buttonDesc: String?,
    buttonTips: String?,
    onclick: () -> Unit
) {
    var isEnabled =
        (buttonStatus == BUTTON_ENABLE) || (buttonStatus == BUTTON_HAVE_SIGNUP) // 控制是否可点击
    val backgroundBrush = if (isEnabled) {
        Brush.horizontalGradient( // 水平渐变色（可改为 verticalGradient）
            colors = listOf(FMColor.color_f4003b, FMColor.color_e40137) // 可点击渐变
        )
    } else {
        SolidColor(FMColor.color_dedede) // 不可点击时纯灰色
    }
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(color = FMColor.color_white)
            .padding(horizontal = 12.dp)

    ) {
        Box(Modifier.size(10.dp))
        if (!buttonTips.isNullOrEmpty()) {
            Text(
                text = getButtonTopText(buttonStatus, buttonTips),
                color = FMColor.color_999999,
                fontSize = 12.sp,
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center
            )
            Box(Modifier.size(8.dp))
        }
        Text(
            text = buttonDesc ?: "",
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = FMColor.color_white,
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth()
                .clickable(enabled = isEnabled) {
                    //点击操作
                    onclick()
                }
                .background(
                    brush = backgroundBrush,
                    shape = RoundedCornerShape(6.dp)
                )
                .padding(vertical = 12.dp),

            )
        Box(Modifier.size(11.dp))
    }
}

@Composable
private fun buildInfoList(activityList: List<InfoDTO>) {
    Column {
        for (i in activityList.indices) {
            Row(
                Modifier.fillMaxWidth()
                    .padding(
                        start = 20.dp,
                        top = 4.dp,
                        bottom = if (i == activityList.size - 1) 0.dp else 4.dp
                    ),
            ) {
                FMImageView(
                    url = activityList[i].pic ?: "",
                    contentScale = ContentScale.FillBounds,
                    placeHolder = FMImageViewPlaceHolderType.GREY,
                    modifier = Modifier.padding(top = 4.dp).width(11.dp).height(11.dp)
                )
                Box(Modifier.size(4.dp))
                Text(
                    text = activityList[i].desc ?: "",
                    fontSize = 13.sp,
                    lineHeight = 18.5.sp,
                    color = FMColor.color_333333
                )
            }
        }
    }
}

@Composable
private fun getButtonTopText(buttonStatus: Int?, buttonTips: String): AnnotatedString {

    if (buttonStatus != BUTTON_ENABLE) {
        return buildAnnotatedString {
            append(buttonTips)
        }
    }
    val timeList = buttonTips.split("-")
    // 如果给的数据不足显示buttonTip作为缺省
    if (timeList.size < 3) {
        return buildAnnotatedString {
            append(buttonTips)
        }
    }
    return buildAnnotatedString {
        // 距离报名结束仅剩
        append(stringResource(Res.string.experiencing_event_enroll_time) + " ")
        // xx 时，xx 分 xx秒
        withStyle(
            SpanStyle(
                fontWeight = FontWeight.Medium,
                fontSize = 14.sp,
                color = FMColor.color_black
            )
        ) {
            append(timeList[0])
        }
        append(" 天 ")
        withStyle(
            SpanStyle(
                fontWeight = FontWeight.Medium,
                fontSize = 14.sp,
                color = FMColor.color_black
            )
        ) {
            append(timeList[1])
        }
        append(" 时 ")
        withStyle(
            SpanStyle(
                fontWeight = FontWeight.Medium,
                fontSize = 14.sp,
                color = FMColor.color_black
            )
        ) {
            append(timeList[2])
        }
        append(" 分")
    }
}
