package com.rt.memberstore.feature.ui.order.view

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.rt.memberstore.feature.logic.domain.store.order.SelectRefundGoodsStore
import com.rt.memberstore.feature.ui.common.*
import com.rt.memberstore.feature.ui.theme.FMColor

@Composable
fun SelectRefundGoodsItem(
    goods: SelectRefundGoodsStore.GoodsItem,
    onSelectClick: (Boolean) -> Unit,
    onQuantityChange: (Int) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 12.dp, vertical = 6.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(FMColor.color_white)
            .padding(12.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 选择按钮
            if (goods.returnOrNot == 0) {
                FMCheckBox(
                    checked = goods.isSelected,
                    onCheckedChange = onSelectClick
                )
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 商品图片
            Box {
                FMImageView(
                    goods.img,
                    modifier = Modifier.size(60.dp),
                    placeHolder = FMImageViewPlaceHolderType.GREY,
                )
                
                if (goods.returnOrNot == 1) {
                    Box(
                        modifier = Modifier
                            .size(60.dp)
                            .background(Color.Black.copy(alpha = 0.5f)),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "已全部退订",
                            color = Color.White,
                            fontSize = 10.sp
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 商品信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = goods.goodsName,
                    fontSize = 14.sp,
                    color = FMColor.color_333333,
                    maxLines = 2
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = "¥${goods.price}",
                    fontSize = 14.sp,
                    color = FMColor.color_666666
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    if (goods.returnOrNot == 0 && goods.isSelected) {
                        FMQuantitySelector(
                            quantity = goods.selectedQuantity,
                            maxQuantity = goods.qty,
                            onQuantityChange = onQuantityChange
                        )
                    } else {
                        Text(
                            text = "数量: ${if (goods.returnOrNot == 1) 0 else goods.qty}",
                            fontSize = 12.sp,
                            color = FMColor.color_999999
                        )
                    }
                    
                    Text(
                        text = "实付：¥${goods.actualRefundAmount}",
                        fontSize = 14.sp,
                        color = FMColor.color_e60012,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}