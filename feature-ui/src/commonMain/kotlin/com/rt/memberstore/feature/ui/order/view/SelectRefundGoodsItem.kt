/*
 * Copyright (c) 2025. 康成投资（中国）有限公司
 * http://www.rt-mart.com
 * 版权归本公司所有，不得私自使用、拷贝、修改、删除，否则视为侵权
 */

package com.rt.memberstore.feature.ui.order.view

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.rt.memberstore.feature.logic.domain.store.order.SelectRefundGoodsStore
import com.rt.memberstore.feature.ui.common.*
import com.rt.memberstore.feature.ui.theme.FMColor
import memberstore.feature_ui.generated.resources.Res
import memberstore.feature_ui.generated.resources.icon_spec_add
import memberstore.feature_ui.generated.resources.icon_spec_sub
import memberstore.feature_ui.generated.resources.icon_spec_sub_disable
import org.jetbrains.compose.resources.painterResource

/**
 * 选择退订商品项组件
 * 根据需求文档实现商品选择、数量调整等功能
 *
 * @param goods 商品信息
 * @param onSelectClick 选择按钮点击回调
 * @param onQuantityChange 数量变化回调
 */
@Composable
fun SelectRefundGoodsItem(
    goods: SelectRefundGoodsStore.GoodsItem,
    onSelectClick: (Boolean) -> Unit,
    onQuantityChange: (Int) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 12.dp, vertical = 6.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(FMColor.color_white)
            .padding(12.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 选择按钮 - 只有可退商品才显示选择按钮
            if (goods.returnOrNot == 0) {
                Checkbox(
                    checked = goods.isSelected,
                    onCheckedChange = { onSelectClick(it) },
                    colors = CheckboxDefaults.colors(
                        checkedColor = FMColor.color_ff003c,
                        uncheckedColor = FMColor.color_cccccc,
                        checkmarkColor = FMColor.color_white
                    )
                )
            }

            Spacer(modifier = Modifier.width(12.dp))

            // 商品图片
            Box {
                FMImageView(
                    url = goods.img,
                    modifier = Modifier.size(60.dp).clip(RoundedCornerShape(4.dp)),
                    placeHolder = FMImageViewPlaceHolderType.GREY,
                )

                // 已全部退订的蒙层
                if (goods.returnOrNot == 1) {
                    Box(
                        modifier = Modifier
                            .size(60.dp)
                            .clip(RoundedCornerShape(4.dp))
                            .background(Color.Black.copy(alpha = 0.5f)),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "已全部退订",
                            color = Color.White,
                            fontSize = 10.sp,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.width(12.dp))

            // 商品信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                // 商品名称
                Text(
                    text = goods.goodsName,
                    fontSize = 14.sp,
                    color = FMColor.color_333333,
                    maxLines = 2,
                    fontWeight = FontWeight.Normal
                )

                Spacer(modifier = Modifier.height(4.dp))

                // 商品单价
                Text(
                    text = "¥${goods.price}",
                    fontSize = 14.sp,
                    color = FMColor.color_666666
                )

                Spacer(modifier = Modifier.height(8.dp))

                // 数量选择器和实付金额
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 数量选择器或数量显示
                    if (goods.returnOrNot == 0 && goods.isSelected) {
                        // 可退且已选中的商品显示数量选择器
                        QuantitySelector(
                            quantity = goods.selectedQuantity,
                            maxQuantity = goods.qty,
                            onQuantityChange = onQuantityChange
                        )
                    } else {
                        // 其他情况显示数量文本
                        Text(
                            text = "数量: ${if (goods.returnOrNot == 1) 0 else goods.qty}",
                            fontSize = 12.sp,
                            color = FMColor.color_999999
                        )
                    }

                    // 实付金额
                    Text(
                        text = "实付：¥${goods.actualRefundAmount}",
                        fontSize = 14.sp,
                        color = FMColor.color_ff2828,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}

/**
 * 数量选择器组件
 * 实现加减按钮和数量显示
 *
 * @param quantity 当前数量
 * @param maxQuantity 最大数量
 * @param onQuantityChange 数量变化回调
 */
@Composable
private fun QuantitySelector(
    quantity: Int,
    maxQuantity: Int,
    onQuantityChange: (Int) -> Unit
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .border(
                width = 0.5.dp,
                color = FMColor.color_cccccc,
                shape = RoundedCornerShape(4.dp)
            )
            .padding(2.dp)
    ) {
        // 减号按钮
        Box(
            modifier = Modifier
                .size(24.dp)
                .clickable(enabled = quantity > 1) {
                    if (quantity > 1) {
                        onQuantityChange(quantity - 1)
                    }
                }
                .background(
                    color = if (quantity > 1) FMColor.color_white else FMColor.color_f2f2f2,
                    shape = RoundedCornerShape(2.dp)
                ),
            contentAlignment = Alignment.Center
        ) {
            Image(
                painter = painterResource(Res.drawable.icon_spec_sub),
                contentDescription = "减少数量",
                modifier = Modifier.size(12.dp)
            )
        }

        // 数量显示
        Text(
            text = quantity.toString(),
            fontSize = 14.sp,
            color = FMColor.color_333333,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .widthIn(min = 32.dp)
                .padding(horizontal = 4.dp)
        )

        // 加号按钮
        Box(
            modifier = Modifier
                .size(24.dp)
                .clickable(enabled = quantity < maxQuantity) {
                    if (quantity < maxQuantity) {
                        onQuantityChange(quantity + 1)
                    }
                }
                .background(
                    color = if (quantity < maxQuantity) FMColor.color_white else FMColor.color_f2f2f2,
                    shape = RoundedCornerShape(2.dp)
                ),
            contentAlignment = Alignment.Center
        ) {
            Image(
                painter = painterResource(Res.drawable.icon_spec_add),
                contentDescription = "增加数量",
                modifier = Modifier.size(12.dp)
            )
        }
    }
}