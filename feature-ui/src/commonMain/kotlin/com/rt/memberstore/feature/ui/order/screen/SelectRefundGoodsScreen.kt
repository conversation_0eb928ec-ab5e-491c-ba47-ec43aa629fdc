package com.rt.memberstore.feature.ui.order.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.rt.kt.lib.core.presentation.components.statusbox.UIState
import com.rt.kt.lib.core.presentation.components.statusbox.rememberPageState
import com.rt.memberstore.feature.logic.domain.integration.home.ExperienceEventDetailComponent.Output
import com.rt.memberstore.feature.logic.domain.integration.order.SelectRefundGoodsComponent
import com.rt.memberstore.feature.logic.domain.store.order.SelectRefundGoodsStore
import com.rt.memberstore.feature.ui.common.FMColor
import com.rt.memberstore.feature.ui.common.FMTopBar
import com.rt.memberstore.feature.ui.common.FMLoadingView
import com.rt.memberstore.feature.ui.common.FMErrorView
import com.rt.memberstore.feature.ui.common.base.FMPageRoot
import com.rt.memberstore.feature.ui.common.base.FMTitleView
import com.rt.memberstore.feature.ui.order.view.SelectRefundGoodsItem
import com.rt.memberstore.feature.ui.order.view.SelectRefundGoodsBottomBar
import com.rt.memberstore.feature.ui.theme.FMColor
import memberstore.feature_ui.generated.resources.Res
import memberstore.feature_ui.generated.resources.experiencing_event_title
import memberstore.feature_ui.generated.resources.select_refund_goods_title
import org.jetbrains.compose.resources.stringResource

@Composable
fun SelectRefundGoodsScreen(
    component: SelectRefundGoodsComponent
) {
    val state by component.state.collectAsState()

    LaunchedEffect(component) {
        component.labels.collect { label ->
            when (label) {
                is SelectRefundGoodsStore.Label.NavigateToApplyRefund -> {
                    component.output(SelectRefundGoodsComponent.Output.NavigateToApplyRefund(label.selectedGoods))
                }

                is SelectRefundGoodsStore.Label.ShowNetworkError -> {
                    // 处理网络错误
                }
            }
        }
    }
    val stateContainer = rememberPageState<Unit>(UIState.Empty())

    FMPageRoot(
        title = stringResource(Res.string.select_refund_goods_title),
        onBackClick = {
            component.output(SelectRefundGoodsComponent.Output.NavigateBack)
        },
        stateContainer = stateContainer,
        pageInitDataLogic = {
            component.loadPage()
            component.trackBrowse()
//            stateContainer.changeState(UIState.Success(Unit))
        }
    ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(FMColor.color_f2f2f2)
            ) {
                when {
                    state.isLoading -> {
                        FMLoadingView()
                    }

                    state.showError -> {
                        FMErrorView(
                            message = state.errorMessage,
                            onRetryClick = {
                                component.onIntent(SelectRefundGoodsStore.Intent.LoadGoods)
                            }
                        )
                    }

                    else -> {
                        SelectRefundGoodsContent(
                            goodsList = state.goodsList,
                            onIntent = component::onIntent
                        )
                    }
                }
            }
        }
    }

    @Composable
    private fun SelectRefundGoodsContent(
        goodsList: List<SelectRefundGoodsStore.GoodsItem>,
        onIntent: (SelectRefundGoodsStore.Intent) -> Unit
    ) {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(vertical = 12.dp)
        ) {
            items(goodsList) { goods ->
                SelectRefundGoodsItem(
                    goods = goods,
                    onSelectClick = { selected ->
                        onIntent(SelectRefundGoodsStore.Intent.SelectGoods(goods.goodsId, selected))
                    },
                    onQuantityChange = { quantity ->
                        onIntent(
                            SelectRefundGoodsStore.Intent.UpdateQuantity(
                                goods.goodsId,
                                quantity
                            )
                        )
                    }
                )
            }
        }
    }