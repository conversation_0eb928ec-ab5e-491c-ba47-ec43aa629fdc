/*
 * Copyright (c) 2025. 康成投资（中国）有限公司
 * http://www.rt-mart.com
 * 版权归本公司所有，不得私自使用、拷贝、修改、删除，否则视为侵权
 */

package com.rt.memberstore.feature.ui.order.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.fn.kt.lib.loading.LoadingView
import com.fn.kt.lib.toast.Toast
import com.rt.kt.lib.core.presentation.components.statusbox.UIState
import com.rt.kt.lib.core.presentation.components.statusbox.rememberPageState
import com.rt.memberstore.feature.logic.domain.integration.order.SelectRefundGoodsComponent
import com.rt.memberstore.feature.logic.domain.store.order.SelectRefundGoodsStore
import com.rt.memberstore.feature.ui.common.NoDataView
import com.rt.memberstore.feature.ui.common.base.FMPageRoot
import com.rt.memberstore.feature.ui.order.view.SelectRefundGoodsItem
import com.rt.memberstore.feature.ui.order.view.SelectRefundGoodsBottomBar
import com.rt.memberstore.feature.ui.theme.FMColor
import memberstore.feature_ui.generated.resources.Res
import memberstore.feature_ui.generated.resources.pic_empty_4
import memberstore.feature_ui.generated.resources.select_refund_goods_title
import memberstore.feature_ui.generated.resources.system_error_retry
import org.jetbrains.compose.resources.stringResource

/**
 * 选择退订商品页面
 *
 * @param component 选择退订商品组件实例，提供数据和处理用户操作
 */
@Composable
fun SelectRefundGoodsScreen(
    component: SelectRefundGoodsComponent
) {
    val state by component.state.collectAsState()

    // 状态切换器
    val stateContainer = rememberPageState<Unit>(UIState.Empty())

    // 错误状态下的重试回调
    val stateErrorRetry: (() -> Unit) = {
        component.onIntent(SelectRefundGoodsStore.Intent.LoadGoods)
    }

    // 接收处理Label
    LaunchedEffect(component) {
        component.labels.collect { label ->
            when (label) {
                is SelectRefundGoodsStore.Label.NavigateToApplyRefund -> {
                    component.onOutput(SelectRefundGoodsComponent.Output.NavigateToApplyRefund(label.selectedGoods))
                }

                is SelectRefundGoodsStore.Label.ShowNetworkError -> {
                    Toast.showShort("网络异常，请重试")
                }
            }
        }
    }

    FMPageRoot(
        title = stringResource(Res.string.select_refund_goods_title),
        onBackClick = {
            component.onOutput(SelectRefundGoodsComponent.Output.NavigateBack)
        },
        stateContainer = stateContainer,
        pageInitDataLogic = {
            component.onIntent(SelectRefundGoodsStore.Intent.LoadGoods)
            stateContainer.changeState(UIState.Success(Unit))
        },
        stateErrorRetry = stateErrorRetry,
        contentScrollEnabled = false,
        rootPageColumnModifier = { modifier ->
            modifier.fillMaxSize().background(FMColor.color_f2f2f2)
        }
    ) {
        SelectRefundGoodsPage(
            state = state,
            onIntent = component::onIntent
        )
    }

    // 页面的Loading
    if (state.isLoading) {
        LoadingView()
    }
}

/**
 * 选择退订商品页面主体内容
 * @param state 选择退订商品状态
 * @param onIntent 处理意图的回调
 */
@Composable
private fun SelectRefundGoodsPage(
    state: SelectRefundGoodsStore.State,
    onIntent: (SelectRefundGoodsStore.Intent) -> Unit
) {
    Column(modifier = Modifier.fillMaxSize()) {
        Box(modifier = Modifier.weight(1f)) {
            when {
                state.showError -> {
                    // 错误状态
                    SelectRefundGoodsError(
                        message = state.errorMessage,
                        onRetryClick = {
                            onIntent(SelectRefundGoodsStore.Intent.LoadGoods)
                        }
                    )
                }

                state.goodsList.isEmpty() && !state.isLoading -> {
                    // 空数据状态
                    SelectRefundGoodsEmpty()
                }

                else -> {
                    // 有数据状态
                    SelectRefundGoodsContent(
                        goodsList = state.goodsList,
                        onIntent = onIntent
                    )
                }
            }
        }

        // 底部固定按钮区域
        if (state.goodsList.isNotEmpty() && !state.showError) {
            SelectRefundGoodsBottomBar(
                isAllSelected = state.isAllSelected,
                hasSelectedGoods = state.hasSelectedGoods,
                onSelectAllClick = {
                    onIntent(SelectRefundGoodsStore.Intent.SelectAll)
                },
                onConfirmClick = {
                    onIntent(SelectRefundGoodsStore.Intent.ConfirmSelection)
                }
            )
        }
    }
}

/**
 * 空数据页
 */
@Composable
private fun SelectRefundGoodsEmpty() {
    NoDataView(
        image = Res.drawable.pic_empty_4,
        content = stringResource(Res.string.system_error_retry),
    )
}

/**
 * 错误状态页
 */
@Composable
private fun SelectRefundGoodsError(
    message: String,
    onRetryClick: () -> Unit
) {
    NoDataView(
        image = Res.drawable.pic_empty_4,
        content = message.ifEmpty { stringResource(Res.string.system_error_retry) },
        rightButtonText = "重试",
        onRightButtonClick = onRetryClick
    )
}

/**
 * 商品列表内容
 */
@Composable
private fun SelectRefundGoodsContent(
    goodsList: List<SelectRefundGoodsStore.GoodsItem>,
    onIntent: (SelectRefundGoodsStore.Intent) -> Unit
) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(vertical = 12.dp)
    ) {
        items(goodsList) { goods ->
            SelectRefundGoodsItem(
                goods = goods,
                onSelectClick = { selected ->
                    onIntent(SelectRefundGoodsStore.Intent.SelectGoods(goods.goodsId, selected))
                },
                onQuantityChange = { quantity ->
                    onIntent(
                        SelectRefundGoodsStore.Intent.UpdateQuantity(
                            goods.goodsId,
                            quantity
                        )
                    )
                }
            )
        }
    }
}