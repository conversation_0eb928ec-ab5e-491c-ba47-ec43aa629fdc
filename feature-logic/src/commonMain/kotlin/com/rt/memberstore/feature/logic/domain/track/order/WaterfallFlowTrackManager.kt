/*
 * Copyright (c) 2025. 康成投资（中国）有限公司
 * http://www.rt-mart.com
 * 版权归本公司所有，不得私自使用、拷贝、修改、删除，否则视为侵权
 */

package com.rt.memberstore.feature.logic.domain.track.order

import com.rt.memberstore.feature.logic.core.track.PageCol
import com.rt.memberstore.feature.logic.core.track.PageID
import com.rt.memberstore.feature.logic.core.track.TrackHelper
import com.rt.memberstore.feature.logic.data.remote.dto.common.GoodsInfoDTO

/**
 * 商品瀑布流页面埋点
 */
object WaterfallFlowTrackManager {

    /**
     * 页面浏览
     */
     fun browseForPage() {
        TrackHelper.page(
            pageId = PageID.WATERFALL_FLOW,
            pageCol = PageCol.BROWSE_WATERFALL_FLOW,
        )
    }

    /**
     * 瀑布流商品曝光
     */
      fun exposeForItem(goods: GoodsInfoDTO) {
        val remarks = mutableMapOf<String, String>()
        remarks["sku_source"] = goods.skuSource ?: ""
        TrackHelper.expose(
            pageId = PageID.WATERFALL_FLOW,
            pageCol = PageCol.EXPOSE_WATERFALL_FLOW_GOODS,
            colPosContent = goods.skuCode,
            remarks = remarks
        )
    }

    /**
     * 返回顶部按钮曝光
     */
      fun exposeForScrollTop() {
        TrackHelper.expose(
            pageId = PageID.WATERFALL_FLOW,
            pageCol = PageCol.EXPOSE_WATERFALL_FLOW_SCROLL_TOP,
        )
    }

    /**
     * 点击商品跳转商详
     */
      fun clickForItem(goods: GoodsInfoDTO) {
        val remarks = mutableMapOf<String, String>()
        remarks["sku_source"] = goods.skuSource ?: ""
        TrackHelper.event(
            pageId = PageID.WATERFALL_FLOW,
            pageCol = PageCol.CLICK_WATERFALL_FLOW_GOODS,
            colPosContent = goods.skuCode,
            remarks = remarks
        )
    }

    /**
     * 点击加入购物车
     */
      fun clickForItemAddCart(goods: GoodsInfoDTO) {
        val remarks = mutableMapOf<String, String>()
        remarks["sku_source"] = goods.skuSource ?: ""
        TrackHelper.event(
            pageId = PageID.WATERFALL_FLOW,
            pageCol = PageCol.CLICK_WATERFALL_FLOW_ADD_CART,
            colPosContent = goods.skuCode,
            remarks = remarks
        )
    }

    /**
     * 点击购物车按钮
     */
      fun clickForCart() {
        TrackHelper.event(
            pageId = PageID.WATERFALL_FLOW,
            pageCol = PageCol.CLICK_WATERFALL_FLOW_CART,
        )
    }

    /**
     * 点击返回顶部按钮
     */
      fun clickForScrollTop() {
        TrackHelper.event(
            pageId = PageID.WATERFALL_FLOW,
            pageCol = PageCol.CLICK_WATERFALL_FLOW_SCROLL_TOP,
        )
    }

}