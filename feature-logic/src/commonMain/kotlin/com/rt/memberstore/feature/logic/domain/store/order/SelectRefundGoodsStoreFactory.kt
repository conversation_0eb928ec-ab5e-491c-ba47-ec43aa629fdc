package com.rt.memberstore.feature.logic.domain.store.order

import com.arkivanov.mvikotlin.core.store.Store
import com.arkivanov.mvikotlin.core.store.StoreFactory
import com.arkivanov.mvikotlin.extensions.coroutines.CoroutineExecutor
import com.rt.memberstore.feature.logic.data.repository.IDataServiceRepository
import com.rt.memberstore.feature.logic.data.remote.dto.order.SelectRefundGoodsParam
import kotlinx.coroutines.launch
import java.math.BigDecimal
import java.math.RoundingMode

class SelectRefundGoodsStoreFactory(
    private val storeFactory: StoreFactory,
    private val dataRepository: IDataServiceRepository,
    private val orderId: String
) {
    fun provide(): SelectRefundGoodsStore = object : SelectRefundGoodsStore,
        Store<SelectRefundGoodsStore.Intent, SelectRefundGoodsStore.State, SelectRefundGoodsStore.Label> by storeFactory.create(
            name = "SelectRefundGoodsStore",
            initialState = SelectRefundGoodsStore.State(),
            executorFactory = { SelectRefundGoodsExecutor(dataRepository, orderId) }
        ) {}
}

private class SelectRefundGoodsExecutor(
    private val dataRepository: IDataServiceRepository,
    private val orderId: String
) : CoroutineExecutor<SelectRefundGoodsStore.Intent, Unit, SelectRefundGoodsStore.State, SelectRefundGoodsMessage, SelectRefundGoodsStore.Label>() {

    override fun executeIntent(intent: SelectRefundGoodsStore.Intent, getState: () -> SelectRefundGoodsStore.State) {
        when (intent) {
            is SelectRefundGoodsStore.Intent.LoadGoods -> loadGoods()
            is SelectRefundGoodsStore.Intent.SelectGoods -> selectGoods(intent.goodsId, intent.selected, getState())
            is SelectRefundGoodsStore.Intent.UpdateQuantity -> updateQuantity(intent.goodsId, intent.quantity, getState())
            is SelectRefundGoodsStore.Intent.SelectAll -> selectAll(getState())
            is SelectRefundGoodsStore.Intent.ConfirmSelection -> confirmSelection(getState())
        }
    }

    private fun loadGoods() {
        scope.launch {
            dispatch(SelectRefundGoodsMessage.SetLoading(true))
            
            val params = SelectRefundGoodsParam(orderId)
            dataRepository.getSelectRefundGoods(params)
                .collect { result ->
                    result.fold(
                        success = { response ->
                            val goodsItems = response?.goodsList?.map { goods ->
                                SelectRefundGoodsStore.GoodsItem(
                                    goodsId = goods.goodsId,
                                    qty = goods.qty,
                                    itemId = goods.itemId,
                                    itemIdStr = goods.itemIdStr,
                                    img = goods.img,
                                    amt = goods.amt,
                                    price = goods.price,
                                    goodsName = goods.goodsName,
                                    returnOrNot = goods.returnOrNot,
                                    actualRefundAmount = calculateRefundAmount(goods.amt, 1, goods.qty)
                                )
                            } ?: emptyList()
                            
                            dispatch(SelectRefundGoodsMessage.SetGoodsList(goodsItems))
                        },
                        failure = {
                            dispatch(SelectRefundGoodsMessage.SetError(true, it.message ?: "系统异常，请重试"))
                            publish(SelectRefundGoodsStore.Label.ShowNetworkError)
                        }
                    )
                    dispatch(SelectRefundGoodsMessage.SetLoading(false))
                }
        }
    }

    private fun calculateRefundAmount(totalAmount: String, selectedQty: Int, totalQty: Int): String {
        return try {
            val total = BigDecimal(totalAmount)
            val result = total.multiply(BigDecimal(selectedQty)).divide(BigDecimal(totalQty), 2, RoundingMode.HALF_UP)
            result.setScale(2, RoundingMode.HALF_UP).toString()
        } catch (e: Exception) {
            "0.00"
        }
    }
}

sealed class SelectRefundGoodsMessage {
    data class SetLoading(val loading: Boolean) : SelectRefundGoodsMessage()
    data class SetGoodsList(val goodsList: List<SelectRefundGoodsStore.GoodsItem>) : SelectRefundGoodsMessage()
    data class SetError(val showError: Boolean, val message: String) : SelectRefundGoodsMessage()
    data class UpdateGoodsSelection(val goodsId: String, val selected: Boolean) : SelectRefundGoodsMessage()
    data class UpdateGoodsQuantity(val goodsId: String, val quantity: Int) : SelectRefundGoodsMessage()
    data class SetAllSelected(val allSelected: Boolean) : SelectRefundGoodsMessage()
    data class SetHasSelectedGoods(val hasSelected: Boolean) : SelectRefundGoodsMessage()
}