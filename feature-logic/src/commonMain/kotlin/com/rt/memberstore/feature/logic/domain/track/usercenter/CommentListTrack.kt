/*
 * Copyright (c) 2021. 康成投资（中国）有限公司
 *  http://www.rt-mart.com
 *  版权归本公司所有，不得私自使用、拷贝、修改、删除，否则视为侵权
 *
 */

package com.rt.memberstore.feature.logic.domain.track.usercenter

import com.rt.memberstore.feature.logic.core.track.PageCol
import com.rt.memberstore.feature.logic.core.track.PageID
import com.rt.memberstore.feature.logic.core.track.TrackHelper

/**
 * 评论列表埋点
 * <AUTHOR> xiang.chen
 * @version : 1.0
 */
object CommentListTrack {

    /**
     * 我的评价页浏览
     */
    fun reviewsPagePage() {
        TrackHelper.page(
            PageID.MY_REVIEW_PAGE,
            PageCol.MY_REVIEW_PAGE_BROWSE
        )
    }

    /**
     * 评价订单曝光
     */
    fun reviewsOrderExpose(colPosition: Int?) {
        TrackHelper.expose(
            PageID.MY_REVIEW_PAGE,
            PageCol.EVALUATION_ORDER_EXPOSURE,
            colPosition = colPosition?.toString()
        )
    }

    /**
     * 点击查看评价按钮
     */
    fun watchDetailButton(colPosition: Int?, orderString: String?) {
        TrackHelper.event(
            PageID.MY_REVIEW_PAGE,
            PageCol.CLICK_THE_REVIEW_BUTTON,
            colPosition = colPosition?.toString(),
            colPosContent = orderString
        )
    }

    /**
     * 点击查看订单按钮
     */
    fun watchOrderButton(colPosition: Int?, orderString: String?) {
        TrackHelper.event(
            PageID.MY_REVIEW_PAGE,
            PageCol.CLICK_VIEW_ORDER_BUTTON,
            colPosition = colPosition?.toString(),
            colPosContent = orderString
        )
    }
}