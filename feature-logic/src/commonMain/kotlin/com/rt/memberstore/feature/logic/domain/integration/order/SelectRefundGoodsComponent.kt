package com.rt.memberstore.feature.logic.domain.integration.order

import com.arkivanov.decompose.ComponentContext
import com.arkivanov.essenty.lifecycle.subscribe
import com.arkivanov.mvikotlin.core.instancekeeper.getStore
import com.arkivanov.mvikotlin.core.store.StoreFactory
import com.arkivanov.mvikotlin.extensions.coroutines.stateFlow
import com.rt.memberstore.feature.logic.domain.store.order.SelectRefundGoodsStore
import com.rt.memberstore.feature.logic.domain.store.order.SelectRefundGoodsStoreFactory
import com.rt.memberstore.feature.logic.data.repository.IDataServiceRepository
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

class SelectRefundGoodsComponent(
    componentContext: ComponentContext,
    storeFactory: StoreFactory,
    private val orderId: String,
    private val output: (Output) -> Unit
) : ComponentContext by componentContext, KoinComponent {

    private val dataRepository by inject<IDataServiceRepository>()

    private val store: SelectRefundGoodsStore = instanceKeeper.getStore {
        SelectRefundGoodsStoreFactory(
            storeFactory = storeFactory,
            dataRepository = dataRepository,
            orderId = orderId
        ).provide()
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    val state: StateFlow<SelectRefundGoodsStore.State> = store.stateFlow
    val labels: Flow<SelectRefundGoodsStore.Label> = store.labels

    fun onIntent(intent: SelectRefundGoodsStore.Intent) {
        store.accept(intent)
    }

    sealed class Output {
        data class NavigateToApplyRefund(val selectedGoods: List<SelectRefundGoodsStore.GoodsItem>) : Output()
        object NavigateBack : Output()
    }

    init {
        lifecycle.subscribe(
            onCreate = {
                onIntent(SelectRefundGoodsStore.Intent.LoadGoods)
            }
        )
    }
}