package com.rt.memberstore.order.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import com.arkivanov.decompose.defaultComponentContext
import com.rt.memberstore.feature.logic.domain.integration.order.SelectRefundGoodsComponent
import com.rt.memberstore.feature.ui.order.screen.SelectRefundGoodsScreen
import com.rt.memberstore.base.activity.FMBaseActivity
import com.rt.memberstore.feature.logic.core.arch.FMStoreFactoryBuilder

/**
 * 退订商品选择页面
 */
class SelectRefundGoodsActivity : FMBaseActivity() {

    companion object {
        private const val EXTRA_ORDER_ID = "extra_order_id"

        fun start(context: Context, orderId: String) {
            val intent = Intent(context, SelectRefundGoodsActivity::class.java)
            intent.putExtra(EXTRA_ORDER_ID, orderId)
            context.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val orderId = intent.getStringExtra(EXTRA_ORDER_ID) ?: ""

        val component = SelectRefundGoodsComponent(
            componentContext = defaultComponentContext(),
            storeFactory = FMStoreFactoryBuilder.defaultStoreFactory(),
            orderId = orderId,
            output = ::onOutput
        )

        setContent {
            SelectRefundGoodsScreen(component = component)
        }
    }

    private fun onOutput(output: SelectRefundGoodsComponent.Output) {
        when (output) {
            is SelectRefundGoodsComponent.Output.NavigateToApplyRefund -> {
                // 跳转到申请退订页面
                // ApplyRefundActivity.start(this, output.selectedGoods)
                finish()
            }

            is SelectRefundGoodsComponent.Output.NavigateBack -> {
                finish()
            }
        }
    }
}