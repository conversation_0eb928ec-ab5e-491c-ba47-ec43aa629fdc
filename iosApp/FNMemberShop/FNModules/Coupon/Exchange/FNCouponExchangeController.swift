//
//  FNCouponExchangeController.swift
//  FNMemberShop
//
//  Created by Clear on 2025/6/19.
//  Copyright © 2025 Feiniu E-commerce(shanghai)Co.Ltd. All rights reserved.
//

import Foundation
import SwiftUI
import KmpShardApp
import FNScan
import FNCore

/// 兑换优惠券页
class FNCouponExchangeController: FNComposeUIViewController {
    // ComponentContext
    private var component : CouponExchangeComponent? = nil
    
    /// 构造ComposeUIViewController
    override func initComposeHostingViewController() -> UIViewController? {
        let _component = CouponExchangeComponent(
            componentContext: DefaultComponentContext(lifecycle: LifecycleRegistryKt.LifecycleRegistry()),
            storeFactory: DefaultStoreFactory(),
            output: {[weak self] output in
                self?.handleOutput(output: output)
            }
        )
        component = _component
        return CouponExchangeViewControllerKt.CouponExchangeViewController(component: _component)
    }
    
    /// 处理Component中的Output
    private func handleOutput(output: CouponExchangeComponent.Output) {
        switch output {
        case is CouponExchangeComponent.OutputClose:
            // 关闭页面
            super.back()
        case is CouponExchangeComponent.OutputJumpToScan:
            // 打开扫码页
            goToScan {[weak self] code in
                guard let strongSelf = self else { return }
                strongSelf.handleIntent(intent: CouponExchangeStoreIntent.OnScanResult(code: code))
            }
        default:
            break
        }
    }
    
    /// 处理与KMP内部的交互
    private func handleIntent(intent: CouponExchangeStoreIntent) {
        component?.onIntent(intent: intent)
    }
    
    /// 打开扫码页
    private func goToScan(_ result: ((String)->())?) {
        guard let vc = FNSCanViewController.memberScan(btnTagTypeBlock: { type, content, this, _ in
            if type == .scanApear {
                this?.navigationController?.popViewController(animated: true)
                result?(content ?? "")
            }
        }) else  { return }
        
        vc.with {
            $0.explainText = "请将二维码/一维码放入框内"
            $0.fnPreferNavigationBarHidden = true
            $0.modalPresentationStyle = .fullScreen
            $0.btnTypeArray = []
        }
        
        FNRouter.shared.pushViewController(vc, animated: true)
    }
}
